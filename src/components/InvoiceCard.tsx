import React, { useState, useRef } from 'react';
import { InvoiceItem } from './TaskBoard';

interface InvoiceCardProps {
  invoice: InvoiceItem;
  onDragStart: (e: React.DragEvent, invoice: InvoiceItem) => void;
  onDragEnd: () => void;
  onStatusChange: (invoiceId: string, newStatus: string) => void;
}

const InvoiceCard: React.FC<InvoiceCardProps> = ({ 
  invoice, 
  onDragStart, 
  onDragEnd, 
  onStatusChange 
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);

  const handleDragStart = (e: React.DragEvent) => {
    setIsDragging(true);
    
    // Add ghost image effect
    if (cardRef.current) {
      const rect = cardRef.current.getBoundingClientRect();
      const ghostImage = cardRef.current.cloneNode(true) as HTMLDivElement;
      ghostImage.style.position = 'absolute';
      ghostImage.style.top = '-1000px';
      ghostImage.style.opacity = '0.8';
      document.body.appendChild(ghostImage);
      e.dataTransfer.setDragImage(ghostImage, rect.width / 2, rect.height / 2);
      
      // Clean up the ghost element after drag
      setTimeout(() => {
        document.body.removeChild(ghostImage);
      }, 0);
    }
    
    onDragStart(e, invoice);
  };

  const handleDragEnd = () => {
    setIsDragging(false);
    onDragEnd();
  };
  
  // Generate tag background class
  const getTagClass = (color: string) => {
    const colorMap: { [key: string]: string } = {
      'blue': 'bg-blue-500/10 text-blue-400 border border-blue-500/20',
      'purple': 'bg-purple-500/10 text-purple-400 border border-purple-500/20',
      'green': 'bg-green-500/10 text-green-400 border border-green-500/20',
      'orange': 'bg-orange-500/10 text-orange-400 border border-orange-500/20',
      'red': 'bg-red-500/10 text-red-400 border border-red-500/20',
    };
    return colorMap[color] || 'bg-muted/50 text-muted-foreground border border-border';
  };

  // Format amount with commas
  const formatAmount = (amount: number) => {
    return amount.toLocaleString('he-IL');
  };

  // Get status color
  const getStatusColor = (status: string) => {
    const statusColors: { [key: string]: string } = {
      'draft': 'text-muted-foreground',
      'sent': 'text-blue-400',
      'paid': 'text-green-400',
      'overdue': 'text-red-400'
    };
    return statusColors[status] || 'text-muted-foreground';
  };

  return (
    <div
      ref={cardRef}
      draggable
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      className={`task-card p-4 bg-card rounded-md border border-border shadow-sm hover:shadow-md transition-all duration-200 h-44 flex flex-col ${isDragging ? 'dragging' : ''}`}
    >
      {/* Header with tag and due date */}
      <div className="flex justify-between items-start mb-3 flex-shrink-0">
        <span className={`text-xs font-medium px-2 py-1 rounded-full ${getTagClass(invoice.tag.color)}`}>
          {invoice.tag.label}
        </span>
        <span className="text-muted-foreground text-xs">{invoice.dueDate}</span>
      </div>
      
      {/* Main content area */}
      <div className="flex-1 flex flex-col min-h-0">
        {/* Title and description */}
        <div className="flex-1 mb-3">
          <h5 className="font-medium mb-1 text-foreground text-sm leading-tight">{invoice.title}</h5>
          <p className="text-xs text-muted-foreground line-clamp-2 leading-relaxed mb-2">{invoice.description}</p>
          <p className="text-xs text-muted-foreground">{invoice.client}</p>
        </div>
        
        {/* Footer with amount and status */}
        <div className="flex justify-between items-center flex-shrink-0 mt-auto">
          <div className="flex items-center gap-1">
            <span className="text-lg font-bold text-foreground">
              {formatAmount(invoice.amount)}
            </span>
            <span className="text-sm text-muted-foreground">{invoice.currency}</span>
          </div>
          
          <div className="flex items-center gap-1">
            {invoice.status === 'paid' ? (
              <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-green-400">
                <path d="M5 12L10 17L19 8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            ) : invoice.status === 'overdue' ? (
              <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-red-400">
                <path d="M12 8V12L15 15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
              </svg>
            ) : invoice.status === 'sent' ? (
              <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-blue-400">
                <path d="M22 2L11 13" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M22 2L15 22L11 13L2 9L22 2Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            ) : (
              <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-muted-foreground">
                <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M14 2V8H20" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default InvoiceCard;
