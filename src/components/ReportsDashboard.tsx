import React from 'react';

const ReportsDashboard: React.FC = () => {
  // Sample data for reports
  const monthlyData = [
    { month: 'ינואר', income: 85000, expenses: 25000 },
    { month: 'פברואר', income: 92000, expenses: 28000 },
    { month: 'מרץ', income: 78000, expenses: 22000 },
    { month: 'אפריל', income: 105000, expenses: 31000 },
    { month: 'מאי', income: 118000, expenses: 35000 },
    { month: 'יוני', income: 134000, expenses: 38000 }
  ];

  const currentMonth = monthlyData[monthlyData.length - 1];
  const previousMonth = monthlyData[monthlyData.length - 2];
  const profit = currentMonth.income - currentMonth.expenses;
  const profitGrowth = ((profit - (previousMonth.income - previousMonth.expenses)) / (previousMonth.income - previousMonth.expenses)) * 100;

  const formatAmount = (amount: number) => {
    return amount.toLocaleString('he-IL');
  };

  const topCategories = [
    { name: 'פיתוח אתרים', amount: 45000, percentage: 35 },
    { name: 'ייעוץ עסקי', amount: 32000, percentage: 25 },
    { name: 'עיצוב גרפי', amount: 28000, percentage: 22 },
    { name: 'שיווק דיגיטלי', amount: 23000, percentage: 18 }
  ];

  return (
    <div className="p-4 space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-card rounded-lg border border-border p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">הכנסות החודש</p>
              <p className="text-2xl font-bold text-green-400">{formatAmount(currentMonth.income)} ₪</p>
              <p className="text-xs text-muted-foreground mt-1">
                +{((currentMonth.income - previousMonth.income) / previousMonth.income * 100).toFixed(1)}% מהחודש הקודם
              </p>
            </div>
            <div className="h-12 w-12 rounded-lg bg-green-500/10 flex items-center justify-center">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-green-400">
                <path d="M7 17L17 7M17 7H7M17 7V17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-card rounded-lg border border-border p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">הוצאות החודש</p>
              <p className="text-2xl font-bold text-red-400">{formatAmount(currentMonth.expenses)} ₪</p>
              <p className="text-xs text-muted-foreground mt-1">
                +{((currentMonth.expenses - previousMonth.expenses) / previousMonth.expenses * 100).toFixed(1)}% מהחודש הקודם
              </p>
            </div>
            <div className="h-12 w-12 rounded-lg bg-red-500/10 flex items-center justify-center">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-red-400">
                <path d="M17 7L7 17M7 7H17M7 7V17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-card rounded-lg border border-border p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">רווח נקי</p>
              <p className="text-2xl font-bold text-blue-400">{formatAmount(profit)} ₪</p>
              <p className="text-xs text-muted-foreground mt-1">
                {profitGrowth > 0 ? '+' : ''}{profitGrowth.toFixed(1)}% מהחודש הקודם
              </p>
            </div>
            <div className="h-12 w-12 rounded-lg bg-blue-500/10 flex items-center justify-center">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-blue-400">
                <path d="M12 2V22M17 5H9.5C8.57174 5 7.6815 5.36875 7.02513 6.02513C6.36875 6.6815 6 7.57174 6 8.5C6 9.42826 6.36875 10.3185 7.02513 10.9749C7.6815 11.6312 8.57174 12 9.5 12H14.5C15.4283 12 16.3185 12.3687 16.9749 13.0251C17.6312 13.6815 18 14.5717 18 15.5C18 16.4283 17.6312 17.3185 16.9749 17.9749C16.3185 18.6312 15.4283 19 14.5 19H6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-card rounded-lg border border-border p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">שיעור רווחיות</p>
              <p className="text-2xl font-bold text-purple-400">{((profit / currentMonth.income) * 100).toFixed(1)}%</p>
              <p className="text-xs text-muted-foreground mt-1">
                מהכנסות כוללות
              </p>
            </div>
            <div className="h-12 w-12 rounded-lg bg-purple-500/10 flex items-center justify-center">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-purple-400">
                <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
                <path d="M8 14S9.5 16 12 16S16 14 16 14" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M9 9H9.01M15 9H15.01" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Monthly Trend */}
        <div className="bg-card rounded-lg border border-border p-4">
          <h3 className="font-medium text-foreground mb-4">מגמה חודשית</h3>
          <div className="space-y-3">
            {monthlyData.slice(-4).map((data, index) => {
              const profit = data.income - data.expenses;
              const maxValue = Math.max(...monthlyData.map(d => d.income));
              const incomeWidth = (data.income / maxValue) * 100;
              const expenseWidth = (data.expenses / maxValue) * 100;
              
              return (
                <div key={data.month} className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-foreground">{data.month}</span>
                    <span className="text-muted-foreground">{formatAmount(profit)} ₪</span>
                  </div>
                  <div className="relative h-6 bg-muted/20 rounded-full overflow-hidden">
                    <div 
                      className="absolute top-0 left-0 h-full bg-green-500/30 rounded-full"
                      style={{ width: `${incomeWidth}%` }}
                    />
                    <div 
                      className="absolute top-0 left-0 h-full bg-red-500/30 rounded-full"
                      style={{ width: `${expenseWidth}%` }}
                    />
                  </div>
                </div>
              );
            })}
          </div>
          <div className="flex items-center gap-4 mt-4 text-xs">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-500/30 rounded-full"></div>
              <span className="text-muted-foreground">הכנסות</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-red-500/30 rounded-full"></div>
              <span className="text-muted-foreground">הוצאות</span>
            </div>
          </div>
        </div>

        {/* Top Categories */}
        <div className="bg-card rounded-lg border border-border p-4">
          <h3 className="font-medium text-foreground mb-4">קטגוריות מובילות</h3>
          <div className="space-y-4">
            {topCategories.map((category, index) => (
              <div key={category.name} className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-foreground">{category.name}</span>
                  <span className="text-muted-foreground">{formatAmount(category.amount)} ₪</span>
                </div>
                <div className="relative h-2 bg-muted/20 rounded-full overflow-hidden">
                  <div 
                    className={`absolute top-0 left-0 h-full rounded-full ${
                      index === 0 ? 'bg-blue-500' :
                      index === 1 ? 'bg-purple-500' :
                      index === 2 ? 'bg-green-500' : 'bg-orange-500'
                    }`}
                    style={{ width: `${category.percentage}%` }}
                  />
                </div>
                <div className="text-xs text-muted-foreground">
                  {category.percentage}% מסך ההכנסות
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-card rounded-lg border border-border p-4">
        <h3 className="font-medium text-foreground mb-4">פעולות מהירות</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button className="flex items-center gap-3 p-3 rounded-lg border border-border hover:bg-muted/20 transition-colors">
            <div className="h-10 w-10 rounded-lg bg-blue-500/10 flex items-center justify-center">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-blue-400">
                <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M14 2V8H20M16 13H8M16 17H8M10 9H8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
            <div className="text-left">
              <p className="font-medium text-foreground">ייצא דוח חודשי</p>
              <p className="text-xs text-muted-foreground">PDF או Excel</p>
            </div>
          </button>

          <button className="flex items-center gap-3 p-3 rounded-lg border border-border hover:bg-muted/20 transition-colors">
            <div className="h-10 w-10 rounded-lg bg-green-500/10 flex items-center justify-center">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-green-400">
                <path d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M7 10L12 15L17 10M12 15V3" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
            <div className="text-left">
              <p className="font-medium text-foreground">שלח למס הכנסה</p>
              <p className="text-xs text-muted-foreground">דיווח רבעוני</p>
            </div>
          </button>

          <button className="flex items-center gap-3 p-3 rounded-lg border border-border hover:bg-muted/20 transition-colors">
            <div className="h-10 w-10 rounded-lg bg-purple-500/10 flex items-center justify-center">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-purple-400">
                <path d="M9 17H7C6.46957 17 5.96086 16.7893 5.58579 16.4142C5.21071 16.0391 5 15.5304 5 15V5C5 4.46957 5.21071 3.96086 5.58579 3.58579C5.96086 3.21071 6.46957 3 7 3H17C17.5304 3 18.0391 3.21071 18.4142 3.58579C18.7893 3.96086 19 4.46957 19 5V15C19 15.5304 18.7893 16.0391 18.4142 16.4142C18.0391 16.7893 17.5304 17 17 17H15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M12 15L17 10H7L12 15Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
            <div className="text-left">
              <p className="font-medium text-foreground">שתף עם רואה חשבון</p>
              <p className="text-xs text-muted-foreground">גישה מוגבלת</p>
            </div>
          </button>
        </div>
      </div>
    </div>
  );
};

export default ReportsDashboard;
