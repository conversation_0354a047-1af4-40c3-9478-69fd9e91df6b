import React, { useState, useEffect } from 'react';

const EmailScannerFeature: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [showNotification, setShowNotification] = useState(false);

  const steps = [
    { type: 'email', title: 'חשבונית מספק', sender: '<EMAIL>' },
    { type: 'scan', title: 'סריקת AI...' },
    { type: 'notification', title: 'הוצאה חדשה נוספה!' }
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      if (currentStep < steps.length - 1) {
        setCurrentStep(prev => prev + 1);
        if (currentStep === 1) {
          setTimeout(() => setShowNotification(true), 1000);
        }
      } else {
        setCurrentStep(0);
        setShowNotification(false);
      }
    }, 3000);

    return () => clearInterval(interval);
  }, [currentStep, steps.length]);

  return (
    <div className="flex flex-col lg:flex-row-reverse items-center gap-8 lg:gap-12">
      {/* Content */}
      <div className="flex-1 text-center lg:text-right">
        <div className="inline-flex items-center gap-2 bg-muted/20 text-muted-foreground px-3 py-1 rounded-full text-sm font-medium mb-4">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M22 6L12 13L2 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
          סריקה אוטומטית
        </div>
        
        <h3 className="text-3xl lg:text-4xl font-bold text-foreground mb-4">
          סורק AI לאימיילים
        </h3>
        
        <p className="text-lg text-muted-foreground mb-6 leading-relaxed">
          המערכת סורקת אוטומטית את תיבת הדואר שלכם, מזהה חשבוניות והוצאות ומוסיפה אותן 
          למערכת ללא צורך בהתערבות ידנית
        </p>

        <div className="space-y-4">
          <div className="flex items-center gap-3 justify-center lg:justify-start">
            <span className="text-foreground">זיהוי אוטומטי של חשבוניות</span>
            <div className="h-8 w-8 rounded-full bg-muted/20 flex items-center justify-center">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-muted-foreground">
                <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
          </div>

          <div className="flex items-center gap-3 justify-center lg:justify-start">
            <span className="text-foreground">עיבוד מיידי של נתונים</span>
            <div className="h-8 w-8 rounded-full bg-muted/20 flex items-center justify-center">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-muted-foreground">
                <path d="M13 2L3 14H12L11 22L21 10H12L13 2Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
          </div>

          <div className="flex items-center gap-3 justify-center lg:justify-start">
            <span className="text-foreground">דיוק גבוה בזיהוי</span>
            <div className="h-8 w-8 rounded-full bg-muted/20 flex items-center justify-center">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-muted-foreground">
                <path d="M12 22S8 18 8 14C8 11.7909 9.79086 10 12 10C14.2091 10 16 11.7909 16 14C16 18 12 22 12 22Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <circle cx="12" cy="14" r="3" stroke="currentColor" strokeWidth="2"/>
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Animated Phone */}
      <div className="flex-shrink-0">
        <div className="relative">
          {/* Phone Frame */}
          <div className="w-80 h-[600px] bg-gradient-to-b from-gray-900 to-black rounded-[3rem] p-2 shadow-2xl">
            <div className="w-full h-full bg-white rounded-[2.5rem] overflow-hidden relative">
              {/* Status Bar */}
              <div className="flex justify-between items-center px-6 py-3 text-black text-sm bg-gray-50">
                <span>9:41</span>
                <div className="flex items-center gap-1">
                  <div className="w-4 h-2 border border-black rounded-sm">
                    <div className="w-3 h-1 bg-black rounded-sm m-0.5"></div>
                  </div>
                </div>
              </div>

              {/* Email App */}
              <div className="bg-white h-full">
                {/* Email Header */}
                <div className="bg-blue-600 px-4 py-4 text-white">
                  <h4 className="font-medium text-lg">דואר נכנס</h4>
                </div>

                {/* Email List */}
                <div className="p-4 space-y-3">
                  {/* Email Item */}
                  <div className={`bg-gray-50 rounded-lg p-4 border-r-4 transition-all duration-500 ${
                    currentStep >= 0 ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
                  }`}>
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
                            S
                          </div>
                          <span className="font-medium text-gray-800">ספק חשמל בע"מ</span>
                          {currentStep >= 1 && (
                            <div className="animate-spin w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
                          )}
                        </div>
                        <h5 className="font-medium text-gray-900 mb-1">חשבונית חשמל - יוני 2024</h5>
                        <p className="text-sm text-gray-600">חשבונית מספר 123456 לתקופת יוני 2024. סכום לתשלום: 1,250 ש"ח</p>
                        <div className="flex items-center gap-2 mt-2">
                          <span className="text-xs text-gray-500">09:30</span>
                          <div className="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                            <svg width="8" height="8" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-white">
                              <path d="M19 14C19 15.1046 18.1046 16 17 16H7C5.89543 16 5 15.1046 5 14V10C5 8.89543 5.89543 8 7 8H17C18.1046 8 19 8.89543 19 10V14Z" fill="currentColor"/>
                            </svg>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Other Emails */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
                            M
                          </div>
                          <span className="font-medium text-gray-800">מכולת המרכז</span>
                        </div>
                        <h5 className="font-medium text-gray-900 mb-1">קבלה על קניות</h5>
                        <p className="text-sm text-gray-600">תודה על הקנייה במכולת המרכז...</p>
                        <span className="text-xs text-gray-500">08:15</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Notification Overlay */}
              {showNotification && currentStep >= 2 && (
                <div className="absolute top-20 left-4 right-4 bg-green-500 text-white p-4 rounded-lg shadow-lg animate-slideDown">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-medium">הוצאה חדשה נוספה!</h4>
                      <p className="text-sm opacity-90">חשמל - 1,250 ש"ח</p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Floating AI Icon */}
          <div className="absolute -top-4 -right-4 w-12 h-12 bg-foreground rounded-full flex items-center justify-center">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-background">
              <path d="M9.663 17H4.5C3.11929 17 2 15.8807 2 14.5C2 13.1193 3.11929 12 4.5 12C4.5 8.41015 7.41015 5.5 11 5.5C14.5899 5.5 17.5 8.41015 17.5 12C18.8807 12 20 13.1193 20 14.5C20 15.8807 18.8807 17 17.5 17H12.337" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M13 21L9 17L13 13" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmailScannerFeature;
