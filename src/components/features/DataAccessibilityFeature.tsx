import React, { useState, useEffect } from 'react';

const DataAccessibilityFeature: React.FC = () => {
  const [activeConnection, setActiveConnection] = useState(0);
  const [dataFlow, setDataFlow] = useState(false);

  const connections = [
    { name: 'Excel', icon: '📊', color: 'bg-foreground' },
    { name: 'רואה חשבון', icon: '👨‍💼', color: 'bg-foreground' },
    { name: 'מס הכנסה', icon: '🏛️', color: 'bg-foreground' },
    { name: 'בנק', icon: '🏦', color: 'bg-foreground' }
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setActiveConnection(prev => (prev + 1) % connections.length);
      setDataFlow(true);
      setTimeout(() => setDataFlow(false), 1500);
    }, 3000);

    return () => clearInterval(interval);
  }, [connections.length]);

  return (
    <div className="flex flex-col lg:flex-row items-center gap-8 lg:gap-12">
      {/* Content */}
      <div className="flex-1 text-center lg:text-right">
        <div className="inline-flex items-center gap-2 bg-muted/20 text-muted-foreground px-3 py-1 rounded-full text-sm font-medium mb-4">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M17 21V19C17 17.9391 16.5786 16.9217 15.8284 16.1716C15.0783 15.4214 14.0609 15 13 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <circle cx="9" cy="7" r="4" stroke="currentColor" strokeWidth="2"/>
            <path d="M23 21V19C22.9993 18.1137 22.7044 17.2528 22.1614 16.5523C21.6184 15.8519 20.8581 15.3516 20 15.13" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M16 3.13C16.8604 3.35031 17.623 3.85071 18.1676 4.55232C18.7122 5.25392 19.0078 6.11683 19.0078 7.005C19.0078 7.89318 18.7122 8.75608 18.1676 9.45769C17.623 10.1593 16.8604 10.6597 16 10.88" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
          שליטה מלאה בנתונים
        </div>
        
        <h3 className="text-3xl lg:text-4xl font-bold text-foreground mb-4">
          נגישות נתונים קלה
        </h3>
        
        <p className="text-lg text-muted-foreground mb-6 leading-relaxed">
          הנתונים שלכם נשארים תמיד בשליטתכם. אינטגרציה קלה עם כל משרד רואי חשבון, 
          ייצוא לכל פורמט ושליטה מלאה על המידע העסקי שלכם
        </p>

        <div className="space-y-4">
          <div className="flex items-center gap-3 justify-center lg:justify-start">
            <span className="text-foreground">ייצוא לכל פורמט</span>
            <div className="h-8 w-8 rounded-full bg-muted/20 flex items-center justify-center">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-muted-foreground">
                <path d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M7 10L12 15L17 10M12 15V3" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
          </div>

          <div className="flex items-center gap-3 justify-center lg:justify-start">
            <span className="text-foreground">אינטגרציה עם רואי חשבון</span>
            <div className="h-8 w-8 rounded-full bg-muted/20 flex items-center justify-center">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-muted-foreground">
                <path d="M16 21V19C16 17.9391 15.5786 16.9217 14.8284 16.1716C14.0783 15.4214 13.0609 15 12 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <circle cx="8.5" cy="7" r="4" stroke="currentColor" strokeWidth="2"/>
                <path d="M20 8V13L23 10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
          </div>

          <div className="flex items-center gap-3 justify-center lg:justify-start">
            <span className="text-foreground">בעלות מלאה על הנתונים</span>
            <div className="h-8 w-8 rounded-full bg-muted/20 flex items-center justify-center">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-muted-foreground">
                <path d="M12 22S8 18 8 14C8 11.7909 9.79086 10 12 10C14.2091 10 16 11.7909 16 14C16 18 12 22 12 22Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <circle cx="12" cy="14" r="3" stroke="currentColor" strokeWidth="2"/>
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Animated Data Flow */}
      <div className="flex-shrink-0">
        <div className="relative w-96 h-96">
          {/* Central Hub */}
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-24 h-24 bg-foreground rounded-2xl flex items-center justify-center shadow-2xl">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-background">
              <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M2 17L12 22L22 17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M2 12L12 17L22 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </div>

          {/* Connection Points */}
          {connections.map((connection, index) => {
            const angle = (index * 90) - 45; // Spread around circle
            const radius = 140;
            const x = Math.cos((angle * Math.PI) / 180) * radius;
            const y = Math.sin((angle * Math.PI) / 180) * radius;
            
            return (
              <div key={connection.name}>
                {/* Connection Node */}
                <div
                  className={`absolute w-16 h-16 ${connection.color} rounded-xl flex flex-col items-center justify-center text-background shadow-lg transition-all duration-500 ${
                    activeConnection === index ? 'scale-110 shadow-2xl' : 'scale-100'
                  }`}
                  style={{
                    top: `calc(50% + ${y}px - 32px)`,
                    left: `calc(50% + ${x}px - 32px)`,
                  }}
                >
                  <span className="text-2xl">{connection.icon}</span>
                  <span className="text-xs font-medium mt-1">{connection.name}</span>
                </div>

                {/* Data Flow Line */}
                <svg
                  className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 pointer-events-none"
                  width="300"
                  height="300"
                  viewBox="0 0 300 300"
                >
                  <line
                    x1="150"
                    y1="150"
                    x2={150 + x}
                    y2={150 + y}
                    stroke={activeConnection === index ? 'hsl(var(--foreground))' : '#e5e7eb'}
                    strokeWidth="2"
                    strokeDasharray="5,5"
                    className={`transition-all duration-500 ${
                      activeConnection === index && dataFlow ? 'animate-pulse' : ''
                    }`}
                  />
                </svg>

                {/* Data Particles */}
                {activeConnection === index && dataFlow && (
                  <div
                    className="absolute w-3 h-3 bg-white rounded-full animate-ping"
                    style={{
                      top: `calc(50% + ${y * 0.5}px - 6px)`,
                      left: `calc(50% + ${x * 0.5}px - 6px)`,
                    }}
                  />
                )}
              </div>
            );
          })}

          {/* Data Flow Indicators */}
          <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 flex items-center gap-2 text-sm text-muted-foreground">
            <div className="w-2 h-2 bg-foreground rounded-full animate-pulse"></div>
            <span>סנכרון נתונים פעיל</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DataAccessibilityFeature;
