
import React from 'react';
import PricingCard from '@/components/PricingCard';
import { pricingPlans } from '@/data/pricingPlans';
const Pricing = () => {
  return (
    <section id="pricing" className="w-full py-20 px-6 md:px-12 bg-background">
      <div className="max-w-7xl mx-auto space-y-16">
        <div className="text-center space-y-4 max-w-3xl mx-auto">
          <h2 className="text-3xl md:text-4xl font-medium tracking-tighter text-foreground">
            תמחור שקוף לכל שלב
          </h2>
          <p className="text-muted-foreground text-lg">
            הגדל את הפעילות הפיננסית שלך עם תוכניות שגדלות עם העסק שלך
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {pricingPlans.map((plan, index) => (
            <PricingCard key={index} plan={plan} />
          ))}
        </div>
        
        <div className="text-center text-muted-foreground">
          יש שאלות? <a href="#" className="text-primary hover:underline">צור קשר עם צוות המכירות שלנו</a>
        </div>
      </div>
    </section>
  );
};

export default Pricing;
