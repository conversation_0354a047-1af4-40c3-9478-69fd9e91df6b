
import React, { useState } from 'react';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { ChevronDown, FileText, Receipt, Smartphone, Mail, DollarSign, Camera } from "lucide-react";

const Features = () => {
  const [openFeature, setOpenFeature] = useState<number | null>(null);
  
  const features = [
    {
      title: "חשבוניות לפי חוק ישראלי",
      description: "צרו חשבוניות מס, חשבוניות עסקה וקבלות בהתאם לדרישות רשויות המס בישראל.",
      expandedDescription: "המערכת כוללת את כל סוגי המסמכים הנדרשים לפי חוק ישראלי: חשבונית מס, חשבונית עסקה, קבלה, הזמנה ועוד. כל מסמך נוצר אוטומטית עם כל הפרטים הנדרשים כולל מספר עוסק מורשה, מע״ם וזיכוי מס במקור.",
      icon: (
        <FileText size={24} className="text-cosmic-accent" />
      )
    },
    {
      title: "שליחה בוואטסאפ ואימייל",
      description: "שלחו חשבוניות ישירות ללקוחות בוואטסאפ, וואטסאפ עסקי או אימייל.",
      expandedDescription: "שילוב מלא עם וואטסאפ עסקי ואימייל לשליחת חשבוניות. אפשרות לשלוח הודעות אוטומטיות לקוחות על חשבוניות חדשות, תזכורות תשלום ואישורי קבלת תשלום. התאמה אישית של תבניות הודעות.",
      icon: (
        <Smartphone size={24} className="text-cosmic-accent" />
      )
    },
    {
      title: "מעקב הוצאות חכם",
      description: "צלמו קבלות והעלו מסמכים בקלות עם זיהוי אוטומטי של פרטי ההוצאה.",
      expandedDescription: "סריקה חכמה של קבלות וחשבוניות עם זיהוי אוטומטי של סכומים, תאריכים וספקים. אפשרות לצלם מסמכים ישירות מהנייד עם OCR מתקדם. קטלוג אוטומטי של הוצאות לפי קטגוריות.",
      icon: (
        <Camera size={24} className="text-cosmic-accent" />
      )
    },
    {
      title: "אינטגרציה עם Gmail",
      description: "חיבור אוטומטי לחשבון Gmail לזיהוי וייבוא הוצאות מאימיילים.",
      expandedDescription: "המערכת סורקת אוטומטית את תיבת הדואר שלכם ומזהה חשבוניות ומסמכי הוצאה. ייבוא אוטומטי של קבלות דיגיטליות מחברות כרטיסי אשראי, ספקי שירותים ועוד. חיסכון זמן משמעותי בניהול ההוצאות.",
      icon: (
        <Mail size={24} className="text-cosmic-accent" />
      )
    },
    {
      title: "חישוב מס אוטומטי",
      description: "חישוב אוטומטי של מע״ם, מס הכנסה וביטוח לאומי בהתאם לפרופיל העסק שלכם.",
      expandedDescription: "המערכת מחשבת אוטומטי את כל המסים הנדרשים: מע״ם על מכירות וקניות, מס הכנסה צפוי, ביטוח לאומי ותשלומי חובה נוספים. התראות מוקדמות על מועדי תשלום ודוחות מס.",
      icon: (
        <DollarSign size={24} className="text-cosmic-accent" />
      )
    },
    {
      title: "דוחות ולוח בקרה",
      description: "לוח בקרה מקיף עם דוחות פיננסיים, מעקב אחר תזרים מזומנים וניתוח רווחיות.",
      expandedDescription: "דוחות מפורטים על מכירות, הוצאות, רווחיות לפי לקוח ומוצר. ניתוח תזרים מזומנים צפוי, מעקב אחר חובות ויתרות לקוחות. יצוא דוחות לאקסל וחיבור ישירות לרואה החשבון.",
      icon: (
        <Receipt size={24} className="text-cosmic-accent" />
      )
    }
  ];
  
  const toggleFeature = (index: number) => {
    setOpenFeature(openFeature === index ? null : index);
  };
  
  return (
    <section id="features" className="w-full py-12 md:py-16 px-6 md:px-12">
      <div className="max-w-7xl mx-auto space-y-12">
        <div className="text-center space-y-3 max-w-3xl mx-auto">
          <h2 className="text-3xl md:text-4xl font-medium tracking-tighter">
            כל מה שהעסק שלכם צריך
          </h2>
          <p className="text-cosmic-muted text-lg">
            פתרון מלא לניהול חשבוניות והוצאות המותאם לעסקים בישראל
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <Collapsible
              key={index}
              open={openFeature === index}
              onOpenChange={() => toggleFeature(index)}
              className={`rounded-xl border ${openFeature === index ? 'border-cosmic-light/40' : 'border-cosmic-light/20'} cosmic-gradient transition-all duration-300`}
            >
              <CollapsibleTrigger className="w-full text-left p-6 flex flex-col">
                <div className="flex justify-between items-start">
                  <div className="h-16 w-16 rounded-full bg-cosmic-light/10 flex items-center justify-center mb-6">
                    {feature.icon}
                  </div>
                  <ChevronDown
                    className={`h-5 w-5 text-cosmic-muted transition-transform duration-200 ${
                      openFeature === index ? 'rotate-180' : ''
                    }`}
                  />
                </div>
                <h3 className="text-xl font-medium tracking-tighter mb-3">{feature.title}</h3>
                <p className="text-cosmic-muted">{feature.description}</p>
              </CollapsibleTrigger>
              <CollapsibleContent className="px-6 pb-6 pt-2">
                <div className="pt-3 border-t border-cosmic-light/10">
                  <p className="text-cosmic-muted">{feature.expandedDescription}</p>
                  <div className="mt-4 flex justify-end">
                    <button className="text-cosmic-accent hover:text-cosmic-accent/80 text-sm font-medium">
                      למידע נוסף ←
                    </button>
                  </div>
                </div>
              </CollapsibleContent>
            </Collapsible>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Features;
