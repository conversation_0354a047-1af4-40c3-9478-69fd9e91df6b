import React from 'react';

interface Client {
  id: string;
  name: string;
  email: string;
  phone: string;
  company: string;
  totalInvoices: number;
  totalAmount: number;
  lastInvoice: string;
  status: 'active' | 'inactive' | 'pending';
  avatar: string;
}

const ClientsDashboard: React.FC = () => {
  const clients: Client[] = [
    {
      id: 'client1',
      name: 'דוד כהן',
      email: '<EMAIL>',
      phone: '050-1234567',
      company: 'חברת טכנולוגיה בע"מ',
      totalInvoices: 12,
      totalAmount: 145000,
      lastInvoice: '15 יוני 2024',
      status: 'active',
      avatar: 'DK'
    },
    {
      id: 'client2',
      name: 'שרה לוי',
      email: '<EMAIL>',
      phone: '052-9876543',
      company: 'סטארט-אפ חדשנות',
      totalInvoices: 8,
      totalAmount: 89000,
      lastInvoice: '10 יוני 2024',
      status: 'active',
      avatar: 'של'
    },
    {
      id: 'client3',
      name: 'מ<PERSON><PERSON><PERSON><PERSON> רוזן',
      email: 'mi<PERSON><PERSON>@creative.co.il',
      phone: '054-5555555',
      company: 'אולפן קריאייטיב',
      totalInvoices: 15,
      totalAmount: 67000,
      lastInvoice: '5 יוני 2024',
      status: 'inactive',
      avatar: 'מר'
    },
    {
      id: 'client4',
      name: 'רחל אברהם',
      email: '<EMAIL>',
      phone: '053-7777777',
      company: 'עסק משפחתי',
      totalInvoices: 6,
      totalAmount: 34000,
      lastInvoice: '1 יוני 2024',
      status: 'pending',
      avatar: 'רא'
    },
    {
      id: 'client5',
      name: 'יוסי גולד',
      email: '<EMAIL>',
      phone: '055-3333333',
      company: 'חברת אופנה',
      totalInvoices: 10,
      totalAmount: 78000,
      lastInvoice: '12 יוני 2024',
      status: 'active',
      avatar: 'יג'
    }
  ];

  const totalClients = clients.length;
  const activeClients = clients.filter(client => client.status === 'active').length;
  const totalRevenue = clients.reduce((sum, client) => sum + client.totalAmount, 0);

  const formatAmount = (amount: number) => {
    return amount.toLocaleString('he-IL');
  };

  const getStatusColor = (status: string) => {
    const colors = {
      'active': 'text-green-400 bg-green-500/10 border-green-500/20',
      'inactive': 'text-gray-400 bg-gray-500/10 border-gray-500/20',
      'pending': 'text-yellow-400 bg-yellow-500/10 border-yellow-500/20'
    };
    return colors[status as keyof typeof colors] || 'text-muted-foreground bg-muted/10';
  };

  const getStatusText = (status: string) => {
    const statusMap = {
      'active': 'פעיל',
      'inactive': 'לא פעיל',
      'pending': 'ממתין'
    };
    return statusMap[status as keyof typeof statusMap] || status;
  };

  return (
    <div className="p-4 space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-card rounded-lg border border-border p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">סך לקוחות</p>
              <p className="text-2xl font-bold text-foreground">{totalClients}</p>
            </div>
            <div className="h-12 w-12 rounded-lg bg-blue-500/10 flex items-center justify-center">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-blue-400">
                <path d="M17 21V19C17 17.9391 16.5786 16.9217 15.8284 16.1716C15.0783 15.4214 14.0609 15 13 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <circle cx="9" cy="7" r="4" stroke="currentColor" strokeWidth="2"/>
                <path d="M23 21V19C22.9993 18.1137 22.7044 17.2528 22.1614 16.5523C21.6184 15.8519 20.8581 15.3516 20 15.13" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M16 3.13C16.8604 3.35031 17.623 3.85071 18.1676 4.55232C18.7122 5.25392 19.0078 6.11683 19.0078 7.005C19.0078 7.89318 18.7122 8.75608 18.1676 9.45769C17.623 10.1593 16.8604 10.6597 16 10.88" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-card rounded-lg border border-border p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">לקוחות פעילים</p>
              <p className="text-2xl font-bold text-green-400">{activeClients}</p>
            </div>
            <div className="h-12 w-12 rounded-lg bg-green-500/10 flex items-center justify-center">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-green-400">
                <path d="M16 21V19C16 17.9391 15.5786 16.9217 14.8284 16.1716C14.0783 15.4214 13.0609 15 12 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <circle cx="8.5" cy="7" r="4" stroke="currentColor" strokeWidth="2"/>
                <path d="M20 8V13L23 10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-card rounded-lg border border-border p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">סך הכנסות</p>
              <p className="text-2xl font-bold text-foreground">{formatAmount(totalRevenue)} ₪</p>
            </div>
            <div className="h-12 w-12 rounded-lg bg-purple-500/10 flex items-center justify-center">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-purple-400">
                <path d="M12 2V22M17 5H9.5C8.57174 5 7.6815 5.36875 7.02513 6.02513C6.36875 6.6815 6 7.57174 6 8.5C6 9.42826 6.36875 10.3185 7.02513 10.9749C7.6815 11.6312 8.57174 12 9.5 12H14.5C15.4283 12 16.3185 12.3687 16.9749 13.0251C17.6312 13.6815 18 14.5717 18 15.5C18 16.4283 17.6312 17.3185 16.9749 17.9749C16.3185 18.6312 15.4283 19 14.5 19H6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Clients List */}
      <div className="bg-card rounded-lg border border-border">
        <div className="p-4 border-b border-border">
          <h3 className="font-medium text-foreground">רשימת לקוחות</h3>
        </div>
        <div className="divide-y divide-border">
          {clients.map((client) => (
            <div key={client.id} className="p-4 hover:bg-muted/20 transition-colors">
              <div className="flex items-center gap-4">
                {/* Avatar */}
                <div className="h-12 w-12 rounded-full bg-muted flex items-center justify-center text-foreground font-medium">
                  {client.avatar}
                </div>
                
                {/* Client Info */}
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-1">
                    <h4 className="font-medium text-foreground">{client.name}</h4>
                    <span className={`text-xs px-2 py-1 rounded-full border ${getStatusColor(client.status)}`}>
                      {getStatusText(client.status)}
                    </span>
                  </div>
                  <p className="text-sm text-muted-foreground mb-1">{client.company}</p>
                  <div className="flex items-center gap-4 text-xs text-muted-foreground">
                    <span>{client.email}</span>
                    <span>{client.phone}</span>
                  </div>
                </div>
                
                {/* Stats */}
                <div className="text-left space-y-1">
                  <p className="text-sm text-muted-foreground">
                    {client.totalInvoices} חשבוניות
                  </p>
                  <p className="text-lg font-bold text-foreground">
                    {formatAmount(client.totalAmount)} ₪
                  </p>
                  <p className="text-xs text-muted-foreground">
                    אחרונה: {client.lastInvoice}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ClientsDashboard;
