
import React, { useState, useEffect } from 'react';
import { useToast } from "@/hooks/use-toast";

// Invoice/Financial data interfaces
export interface InvoiceItem {
  id: string;
  title: string;
  description: string;
  amount: number;
  status: 'draft' | 'sent' | 'paid' | 'overdue';
  tag: {
    color: string;
    label: string;
  };
  dueDate: string;
  client: string;
  currency: string;
}

export interface MetricColumn {
  id: string;
  title: string;
  color: string;
  totalAmount: number;
  items: InvoiceItem[];
}

// Initial data for the invoice dashboard
const initialColumns: MetricColumn[] = [
  {
    id: 'draft',
    title: 'טיוטות',
    color: 'muted',
    totalAmount: 45600,
    items: [
      {
        id: 'inv1',
        title: 'חשבונית #1001',
        description: 'שירותי פיתוח אתר אינטרנט',
        amount: 18500,
        status: 'draft',
        tag: { color: 'blue', label: 'פיתוח' },
        dueDate: '15 יוני',
        client: 'חברת טכנולוגיה בע"מ',
        currency: '₪'
      },
      {
        id: 'inv2',
        title: 'חשבונית #1002',
        description: 'ייעוץ עסקי ואסטרטגיה דיגיטלית',
        amount: 12300,
        status: 'draft',
        tag: { color: 'purple', label: 'ייעוץ' },
        dueDate: '20 יוני',
        client: 'סטארט-אפ חדשנות',
        currency: '₪'
      },
      {
        id: 'inv3',
        title: 'חשבונית #1003',
        description: 'עיצוב גרפי ומיתוג',
        amount: 8900,
        status: 'draft',
        tag: { color: 'green', label: 'עיצוב' },
        dueDate: '25 יוני',
        client: 'אולפן קריאייטיב',
        currency: '₪'
      },
      {
        id: 'inv4',
        title: 'חשבונית #1004',
        description: 'שירותי תחזוקה חודשיים',
        amount: 5900,
        status: 'draft',
        tag: { color: 'orange', label: 'תחזוקה' },
        dueDate: '30 יוני',
        client: 'עסק משפחתי',
        currency: '₪'
      }
    ]
  },
  {
    id: 'sent',
    title: 'נשלחו',
    color: 'blue',
    totalAmount: 67800,
    items: [
      {
        id: 'inv5',
        title: 'חשבונית #0998',
        description: 'פיתוח אפליקציה מובילה',
        amount: 35000,
        status: 'sent',
        tag: { color: 'blue', label: 'פיתוח' },
        dueDate: '10 יוני',
        client: 'חברת הייטק גדולה',
        currency: '₪'
      },
      {
        id: 'inv6',
        title: 'חשבונית #0999',
        description: 'קמפיין שיווק דיגיטלי',
        amount: 22800,
        status: 'sent',
        tag: { color: 'purple', label: 'שיווק' },
        dueDate: '12 יוני',
        client: 'חברת אופנה',
        currency: '₪'
      },
      {
        id: 'inv7',
        title: 'חשבונית #1000',
        description: 'עיצוב ממשק משתמש',
        amount: 10000,
        status: 'sent',
        tag: { color: 'green', label: 'עיצוב' },
        dueDate: '8 יוני',
        client: 'סטודיו עיצוב',
        currency: '₪'
      }
    ]
  },
  {
    id: 'overdue',
    title: 'באיחור',
    color: 'red',
    totalAmount: 28900,
    items: [
      {
        id: 'inv8',
        title: 'חשבונית #0995',
        description: 'שירותי ייעוץ טכנולוגי',
        amount: 15400,
        status: 'overdue',
        tag: { color: 'purple', label: 'ייעוץ' },
        dueDate: '25 מאי',
        client: 'חברת ייעוץ',
        currency: '₪'
      },
      {
        id: 'inv9',
        title: 'חשבונית #0996',
        description: 'פיתוח מערכת ניהול',
        amount: 13500,
        status: 'overdue',
        tag: { color: 'blue', label: 'פיתוח' },
        dueDate: '20 מאי',
        client: 'עסק קטן',
        currency: '₪'
      }
    ]
  },
  {
    id: 'paid',
    title: 'שולמו',
    color: 'green',
    totalAmount: 89200,
    items: [
      {
        id: 'inv10',
        title: 'חשבונית #0992',
        description: 'פרויקט אתר מסחר אלקטרוני',
        amount: 45000,
        status: 'paid',
        tag: { color: 'blue', label: 'פיתוח' },
        dueDate: '15 מאי',
        client: 'חנות אונליין',
        currency: '₪'
      },
      {
        id: 'inv11',
        title: 'חשבונית #0993',
        description: 'מערכת ניהול לקוחות',
        amount: 28700,
        status: 'paid',
        tag: { color: 'purple', label: 'מערכות' },
        dueDate: '10 מאי',
        client: 'חברת שירותים',
        currency: '₪'
      },
      {
        id: 'inv12',
        title: 'חשבונית #0994',
        description: 'עיצוב לוגו ומיתוג',
        amount: 15500,
        status: 'paid',
        tag: { color: 'green', label: 'עיצוב' },
        dueDate: '5 מאי',
        client: 'חברה חדשה',
        currency: '₪'
      }
    ]
  }
];

import InvoiceColumn from './InvoiceColumn';

interface InvoiceDashboardProps {
  className?: string;
}

const InvoiceDashboard: React.FC<InvoiceDashboardProps> = ({ className }) => {
  const [columns, setColumns] = useState<MetricColumn[]>(initialColumns);
  const [draggedInvoice, setDraggedInvoice] = useState<InvoiceItem | null>(null);
  const [dragSourceColumn, setDragSourceColumn] = useState<string | null>(null);
  const { toast } = useToast();

  const handleInvoiceDragStart = (e: React.DragEvent, invoice: InvoiceItem) => {
    e.dataTransfer.setData('invoiceId', invoice.id);
    setDraggedInvoice(invoice);

    // Find source column
    const sourceColumn = columns.find(col =>
      col.items.some(item => item.id === invoice.id)
    );

    if (sourceColumn) {
      setDragSourceColumn(sourceColumn.id);
      e.dataTransfer.setData('sourceColumnId', sourceColumn.id);
    }
  };

  const handleInvoiceDragEnd = () => {
    setDraggedInvoice(null);
    setDragSourceColumn(null);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDragLeave = (e: React.DragEvent) => {
    // Handle drag leave logic if needed
  };

  const handleDrop = (e: React.DragEvent, targetColumnId: string) => {
    e.preventDefault();

    const invoiceId = e.dataTransfer.getData('invoiceId');
    const sourceColumnId = e.dataTransfer.getData('sourceColumnId');

    if (!invoiceId || !sourceColumnId || sourceColumnId === targetColumnId) {
      return;
    }

    // Update columns state and recalculate totals
    const newColumns = columns.map(column => {
      // Remove invoice from source column
      if (column.id === sourceColumnId) {
        const updatedItems = column.items.filter(item => item.id !== invoiceId);
        const newTotal = updatedItems.reduce((sum, item) => sum + item.amount, 0);
        return {
          ...column,
          items: updatedItems,
          totalAmount: newTotal
        };
      }

      // Add invoice to target column
      if (column.id === targetColumnId) {
        const invoiceToMove = columns.find(col => col.id === sourceColumnId)?.items.find(item => item.id === invoiceId);
        if (invoiceToMove) {
          const updatedInvoice = { ...invoiceToMove, status: targetColumnId as any };
          const updatedItems = [...column.items, updatedInvoice];
          const newTotal = updatedItems.reduce((sum, item) => sum + item.amount, 0);
          return {
            ...column,
            items: updatedItems,
            totalAmount: newTotal
          };
        }
      }

      return column;
    });

    setColumns(newColumns);

    // Show a toast notification
    const targetColumn = columns.find(col => col.id === targetColumnId);
    if (targetColumn && draggedInvoice) {
      toast({
        title: "חשבונית הועברה",
        description: `${draggedInvoice.title} הועברה ל${targetColumn.title}`,
      });
    }
  };

  const handleStatusChange = (invoiceId: string, newStatus: string) => {
    // This function can be used for programmatic status changes (not used in this implementation)
  };

  return (
    <div className="p-4">
      {/* Board Header */}
      <div className="flex items-center justify-between mb-6 min-w-0">
        <div className="flex items-center gap-2 flex-shrink-0">
          <h3 className="font-medium text-foreground">חשבוניות החודש</h3>
          <span className="text-xs bg-muted px-2 py-1 rounded-full text-muted-foreground">
            {columns.reduce((total, col) => total + col.items.length, 0)}
          </span>
        </div>

        <div className="flex items-center gap-2 flex-shrink-0">
          <div className="h-8 w-8 rounded-md bg-muted flex items-center justify-center text-muted-foreground">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M15 12H9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
              <path d="M12 9L12 15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
            </svg>
          </div>
          <div className="h-8 w-8 rounded-md bg-muted flex items-center justify-center text-muted-foreground">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M17 9L17 17H9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
              <path d="M17 17L7 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
            </svg>
          </div>
          <div className="h-8 px-3 rounded-md bg-foreground text-background flex items-center justify-center text-sm font-medium whitespace-nowrap">
            חשבונית חדשה
          </div>
        </div>
      </div>

      {/* Invoice Columns */}
      <div className={`flex gap-4 overflow-x-auto pb-4 ${className}`}>
        {columns.map(column => (
          <InvoiceColumn
            key={column.id}
            column={column}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onInvoiceDragStart={handleInvoiceDragStart}
            onInvoiceDragEnd={handleInvoiceDragEnd}
            onStatusChange={handleStatusChange}
          />
        ))}
      </div>
    </div>
  );
};

export default InvoiceDashboard;
