import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import InvoiceDashboard from './TaskBoard';
import ExpensesDashboard from './ExpensesDashboard';
import ClientsDashboard from './ClientsDashboard';
import ReportsDashboard from './ReportsDashboard';
import { Loader } from 'lucide-react';
const HeroSection = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [activeTab, setActiveTab] = useState('invoices');

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 300);
    return () => clearTimeout(timer);
  }, []);

  const handleNavClick = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const tabs = [
    { id: 'invoices', label: 'חשבוניות', icon: '📄' },
    { id: 'expenses', label: 'הוצאות', icon: '💰' },
    { id: 'clients', label: 'לקוחות', icon: '👥' },
    { id: 'reports', label: 'דוחות', icon: '📊' }
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'invoices':
        return <InvoiceDashboard />;
      case 'expenses':
        return <ExpensesDashboard />;
      case 'clients':
        return <ClientsDashboard />;
      case 'reports':
        return <ReportsDashboard />;
      default:
        return <InvoiceDashboard />;
    }
  };
  return <section className="relative w-full py-12 md:py-20 px-6 md:px-12 flex flex-col items-center justify-center overflow-hidden bg-background" dir="rtl">
      {/* Cosmic particle effect (background dots) */}
      <div className="absolute inset-0 cosmic-grid opacity-30"></div>
      
      {/* Gradient glow effect */}
      <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] rounded-full">
        <div className="w-full h-full opacity-10 bg-primary blur-[120px]"></div>
      </div>
      
      <div className={`relative z-10 max-w-4xl text-center space-y-6 transition-all duration-700 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
        <div className="flex justify-center">
          <span className="inline-flex items-center gap-2 px-3 py-1 text-xs font-medium rounded-full bg-muted text-primary">
            <span className="flex h-2 w-2 rounded-full bg-primary"></span>
            חדש: סריקת מסמכים חכמה
            <Loader className="h-3 w-3 animate-spin text-primary" />
          </span>
        </div>
        
        <h1 className="text-4xl md:text-6xl lg:text-7xl font-medium tracking-tighter text-balance text-foreground">
          מערכת חשבוניות <span className="text-foreground">מתקדמת</span> לעסקים בישראל
        </h1>
        
        <p className="text-lg md:text-xl text-muted-foreground max-w-2xl mx-auto text-balance">
          צרו חשבוניות, עקבו אחר הוצאות ונהלו את העסק שלכם בקלות. מותאם לחוק הישראלי עם אפשרות שליחה בוואטסאפ ואימייל.
        </p>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center pt-6 items-center">
          <Button
            onClick={() => handleNavClick('features')}
            className="bg-primary text-primary-foreground hover:bg-primary/80 hover:text-primary-foreground text-base h-12 px-8 transition-all duration-200 min-h-[48px]"
          >
            תכונות
          </Button>
          <Button
            onClick={() => handleNavClick('dashboard')}
            variant="outline"
            className="border-border text-foreground hover:bg-accent hover:text-accent-foreground text-base h-12 px-8 transition-all duration-200 min-h-[48px]"
          >
            לוח בקרה
          </Button>
        </div>
        
        <div className="pt-6 text-sm text-muted-foreground">
          ללא צורך בכרטיס אשראי • ניסיון בחינם למשך 14 יום
        </div>
      </div>
      
      {/* Task Manager UI integrated in hero section with glassmorphic effect */}
      <div id="dashboard" className={`w-full max-w-7xl mt-12 z-10 transition-all duration-1000 delay-500 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-20'}`}>
        <div className="cosmic-glow relative rounded-xl overflow-hidden border border-border backdrop-blur-sm bg-card shadow-lg">
          {/* Dashboard Header */}
          <div className="bg-card backdrop-blur-md w-full">
            <div className="flex items-center justify-between p-4 border-b border-border">
              <div className="flex items-center gap-4">
                <div className="h-8 w-8 rounded-md bg-muted flex items-center justify-center">
                  <div className="h-3 w-3 rounded-sm bg-foreground"></div>
                </div>
                <span className="text-foreground font-medium">ניהול חשבוניות והוצאות</span>
              </div>
              
              <div className="flex items-center gap-3">
                <div className="flex -space-x-2">
                  <div className="h-8 w-8 rounded-full bg-muted border-2 border-card"></div>
                  <div className="h-8 w-8 rounded-full bg-muted/80 border-2 border-card"></div>
                  <div className="h-8 w-8 rounded-full bg-muted/60 border-2 border-card"></div>
                  <div className="h-8 w-8 rounded-full bg-muted/40 border-2 border-card flex items-center justify-center text-xs text-foreground">+3</div>
                </div>
                
                <div className="h-8 px-3 rounded-md bg-muted flex items-center justify-center text-foreground text-sm">
                  שתף
                </div>
              </div>
            </div>
            
            {/* Dashboard Content */}
            <div className="flex h-[600px] overflow-hidden">
              {/* Sidebar */}
              <div className="w-64 border-r border-border p-4 space-y-4 hidden md:block bg-card">
                <div className="space-y-2">
                  <div className="text-xs text-muted-foreground uppercase">ניווט</div>
                  <div className="space-y-1">
                    {tabs.map((tab) => (
                      <button
                        key={tab.id}
                        onClick={() => setActiveTab(tab.id)}
                        className={`w-full flex items-center gap-3 px-3 py-2 rounded-md transition-colors ${
                          activeTab === tab.id
                            ? 'bg-muted text-foreground'
                            : 'text-muted-foreground hover:bg-muted/50 hover:text-foreground'
                        }`}
                      >
                        <div className={`h-3 w-3 rounded-sm ${
                          activeTab === tab.id ? 'bg-foreground' : 'bg-muted-foreground/30'
                        }`}></div>
                        <span>{tab.label}</span>
                      </button>
                    ))}
                  </div>
                </div>
              </div>
              
              {/* Main Content */}
              <div className="flex-1 bg-background overflow-hidden">
                {/* Dynamic Tab Content */}
                {renderTabContent()}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>;
};
export default HeroSection;