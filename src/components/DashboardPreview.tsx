
import React, { useState, useEffect } from 'react';
import { TrendingUp, TrendingDown, DollarSign, FileText, Users, Calendar } from 'lucide-react';

const DashboardPreview = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Use IntersectionObserver to trigger animation when component enters viewport
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      { threshold: 0.2 }
    );

    const section = document.getElementById('dashboard');
    if (section) observer.observe(section);

    return () => {
      if (section) observer.unobserve(section);
    };
  }, []);

  return (
    <section id="dashboard" className="w-full py-20 px-6 md:px-12">
      <div className="max-w-7xl mx-auto space-y-16">
        <div 
          className={`text-center space-y-4 max-w-3xl mx-auto transition-all duration-700 transform ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
        >
          <h2 className="text-3xl md:text-4xl font-medium tracking-tighter">
            לוח בקרה עסקי מתקדם
          </h2>
          <p className="text-cosmic-muted text-lg">
            מעקב אחר הכנסות, הוצאות ומסים בזמן אמת
          </p>
        </div>
        
        <div 
          className={`cosmic-glow relative rounded-xl overflow-hidden border border-white/10 backdrop-blur-sm bg-cosmic-darker/70 shadow-[0_0_15px_rgba(203,255,77,0.15)] transition-all duration-1000 delay-300 ${
            isVisible ? 'opacity-100 scale-100' : 'opacity-0 scale-95'
          }`}
        >
          {/* Mock Dashboard */}
          <div className="bg-cosmic-darker/80 backdrop-blur-md w-full">
            {/* Dashboard Header */}
            <div className="flex items-center justify-between p-4 border-b border-cosmic-light/10">
              <div className="flex items-center gap-4">
                <div className="h-8 w-8 rounded-md bg-cosmic-light/20 flex items-center justify-center">
                  <DollarSign className="h-4 w-4 text-cosmic-accent" />
                </div>
                <span className="text-white font-medium">לוח בקרה עסקי - דצמבר 2024</span>
              </div>
              
              <div className="flex items-center gap-3">
                <div className="h-8 px-3 rounded-md bg-cosmic-accent text-cosmic-darker flex items-center justify-center text-sm font-medium">
                  חשבונית חדשה
                </div>
              </div>
            </div>
            
            {/* Dashboard Content */}
            <div className="flex h-[500px] overflow-hidden">
              {/* Sidebar */}
              <div className="w-64 border-r border-cosmic-light/10 p-4 space-y-4">
                <div className="space-y-2">
                  <div className="text-xs text-cosmic-muted uppercase">ניווט</div>
                  <div className="space-y-1">
                    <div className="flex items-center gap-3 px-3 py-2 rounded-md bg-cosmic-light/10 text-white">
                      <TrendingUp className="h-3 w-3 text-cosmic-accent" />
                      <span>סקירה</span>
                    </div>
                    <div className="flex items-center gap-3 px-3 py-2 rounded-md text-cosmic-muted hover:bg-cosmic-light/5">
                      <FileText className="h-3 w-3" />
                      <span>חשבוניות</span>
                    </div>
                    <div className="flex items-center gap-3 px-3 py-2 rounded-md text-cosmic-muted hover:bg-cosmic-light/5">
                      <Users className="h-3 w-3" />
                      <span>לקוחות</span>
                    </div>
                    <div className="flex items-center gap-3 px-3 py-2 rounded-md text-cosmic-muted hover:bg-cosmic-light/5">
                      <Calendar className="h-3 w-3" />
                      <span>הוצאות</span>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-2 pt-4">
                  <div className="text-xs text-cosmic-muted uppercase">אינטגרציות</div>
                  <div className="space-y-1">
                    <div className="flex items-center gap-3 px-3 py-2 rounded-md text-cosmic-muted hover:bg-cosmic-light/5">
                      <div className="h-3 w-3 rounded-full bg-cosmic-accent/80"></div>
                      <span>Gmail</span>
                    </div>
                    <div className="flex items-center gap-3 px-3 py-2 rounded-md text-cosmic-muted hover:bg-cosmic-light/5">
                      <div className="h-3 w-3 rounded-full bg-green-400/80"></div>
                      <span>WhatsApp</span>
                    </div>
                    <div className="flex items-center gap-3 px-3 py-2 rounded-md text-cosmic-muted hover:bg-cosmic-light/5">
                      <div className="h-3 w-3 rounded-full bg-blue-400/80"></div>
                      <span>מס הכנסה</span>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Main Content */}
              <div className="flex-1 p-4 overflow-y-auto">
                {/* Metrics Grid */}
                <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                  {/* Revenue Card */}
                  <div className="bg-cosmic-light/10 rounded-lg p-4 border border-cosmic-light/20">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-cosmic-muted text-sm">הכנסות החודש</p>
                        <p className="text-white text-xl font-semibold">₪47,200</p>
                      </div>
                      <TrendingUp className="h-8 w-8 text-cosmic-accent" />
                    </div>
                    <div className="flex items-center gap-1 mt-2">
                      <TrendingUp className="h-3 w-3 text-cosmic-accent" />
                      <span className="text-cosmic-accent text-xs">+12% מהחודש הקודם</span>
                    </div>
                  </div>

                  {/* Outstanding Invoices */}
                  <div className="bg-cosmic-light/10 rounded-lg p-4 border border-cosmic-light/20">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-cosmic-muted text-sm">חשבוניות ממתינות</p>
                        <p className="text-white text-xl font-semibold">₪18,900</p>
                      </div>
                      <FileText className="h-8 w-8 text-orange-400" />
                    </div>
                    <div className="flex items-center gap-1 mt-2">
                      <span className="text-orange-400 text-xs">7 חשבוניות</span>
                    </div>
                  </div>

                  {/* Clients */}
                  <div className="bg-cosmic-light/10 rounded-lg p-4 border border-cosmic-light/20">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-cosmic-muted text-sm">לקוחות פעילים</p>
                        <p className="text-white text-xl font-semibold">34</p>
                      </div>
                      <Users className="h-8 w-8 text-blue-400" />
                    </div>
                    <div className="flex items-center gap-1 mt-2">
                      <span className="text-blue-400 text-xs">+3 חדשים השבוע</span>
                    </div>
                  </div>

                  {/* Tax Estimate */}
                  <div className="bg-cosmic-light/10 rounded-lg p-4 border border-cosmic-light/20">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-cosmic-muted text-sm">מס לתשלום</p>
                        <p className="text-white text-xl font-semibold">₪8,100</p>
                      </div>
                      <Calendar className="h-8 w-8 text-red-400" />
                    </div>
                    <div className="flex items-center gap-1 mt-2">
                      <span className="text-red-400 text-xs">תשלום עד 15/1</span>
                    </div>
                  </div>
                </div>

                {/* Recent Activity */}
                <div className="bg-cosmic-light/10 rounded-lg p-4 border border-cosmic-light/20">
                  <h3 className="text-white font-medium mb-4">פעילות אחרונה</h3>
                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      <div className="h-8 w-8 rounded-full bg-cosmic-accent/20 flex items-center justify-center">
                        <FileText className="h-4 w-4 text-cosmic-accent" />
                      </div>
                      <div className="flex-1">
                        <p className="text-white text-sm">חשבונית 2024-156 נשלחה ללקוח אבי כהן</p>
                        <p className="text-cosmic-muted text-xs">לפני 2 שעות</p>
                      </div>
                      <span className="text-cosmic-accent text-sm">₪2,400</span>
                    </div>
                    
                    <div className="flex items-center gap-3">
                      <div className="h-8 w-8 rounded-full bg-green-400/20 flex items-center justify-center">
                        <DollarSign className="h-4 w-4 text-green-400" />
                      </div>
                      <div className="flex-1">
                        <p className="text-white text-sm">תשלום התקבל מלקוח רחל לוי</p>
                        <p className="text-cosmic-muted text-xs">לפני 4 שעות</p>
                      </div>
                      <span className="text-green-400 text-sm">₪1,800</span>
                    </div>
                    
                    <div className="flex items-center gap-3">
                      <div className="h-8 w-8 rounded-full bg-blue-400/20 flex items-center justify-center">
                        <Users className="h-4 w-4 text-blue-400" />
                      </div>
                      <div className="flex-1">
                        <p className="text-white text-sm">לקוח חדש נרשם - דני מלכה</p>
                        <p className="text-cosmic-muted text-xs">אתמול</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default DashboardPreview;
