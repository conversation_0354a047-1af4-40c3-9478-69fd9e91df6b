import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { ChevronRight, ChevronLeft } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';

const surveySchema = z.object({
  companyName: z.string().min(2, 'שם החברה חייב להכיל לפחות 2 תווים'),
  annualRevenue: z.string().min(1, 'יש לבחור הכנסה שנתית'),
  accountingServices: z.boolean(),
  businessLoan: z.boolean(),
});

type SurveyFormData = z.infer<typeof surveySchema>;

interface SignUpSurveyProps {
  onComplete: (data: SurveyFormData) => void;
  onBack?: () => void;
}

const revenueOptions = [
  { value: '0-50000', label: '0-50,000 ₪' },
  { value: '************', label: '50,000-100,000 ₪' },
  { value: '100000-250000', label: '100,000-250,000 ₪' },
  { value: '250000-500000', label: '250,000-500,000 ₪' },
  { value: '500000-1000000', label: '500,000-1,000,000 ₪' },
  { value: '1000000+', label: '1,000,000+ ₪' },
];

const questions = [
  {
    id: 'companyName',
    title: 'מה שם החברה שלך?',
    type: 'text' as const,
    placeholder: 'הזן שם החברה',
  },
  {
    id: 'annualRevenue',
    title: 'מה ההכנסה השנתית של החברה?',
    type: 'select' as const,
    options: revenueOptions,
  },
  {
    id: 'accountingServices',
    title: 'האם תרצה לשמוע על שירותי הנהלת חשבונות שלנו?',
    type: 'boolean' as const,
  },
  {
    id: 'businessLoan',
    title: 'האם תהיה מעוניין בהלוואה עסקית?',
    type: 'boolean' as const,
  },
];

export const SignUpSurvey: React.FC<SignUpSurveyProps> = ({ onComplete, onBack }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
    trigger,
  } = useForm<SurveyFormData>({
    resolver: zodResolver(surveySchema),
    mode: 'onChange',
  });

  const currentQuestion = questions[currentStep];
  const progress = ((currentStep + 1) / questions.length) * 100;
  const watchedValue = watch(currentQuestion.id as keyof SurveyFormData);

  const handleNext = async () => {
    const isValid = await trigger(currentQuestion.id as keyof SurveyFormData);
    
    if (!isValid) return;

    if (currentStep < questions.length - 1) {
      setIsAnimating(true);
      setTimeout(() => {
        setCurrentStep(currentStep + 1);
        setIsAnimating(false);
      }, 150);
    } else {
      handleSubmit(onComplete)();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setIsAnimating(true);
      setTimeout(() => {
        setCurrentStep(currentStep - 1);
        setIsAnimating(false);
      }, 150);
    } else if (onBack) {
      onBack();
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleNext();
    }
  };

  const renderQuestion = () => {
    const question = currentQuestion;
    const fieldName = question.id as keyof SurveyFormData;
    const error = errors[fieldName];

    switch (question.type) {
      case 'text':
      case 'email':
      case 'tel':
        return (
          <div className="space-y-4">
            <Input
              type={question.type}
              placeholder={question.placeholder}
              {...register(fieldName)}
              className={cn(
                'text-lg h-12',
                error && 'border-destructive'
              )}
              onKeyPress={handleKeyPress}
              autoFocus
            />
            {error && (
              <p className="text-sm text-destructive">{error.message}</p>
            )}
          </div>
        );

      case 'select':
        return (
          <div className="space-y-4">
            <Select
              value={watchedValue as string}
              onValueChange={(value) => setValue(fieldName, value as any)}
            >
              <SelectTrigger className="text-lg h-12">
                <SelectValue placeholder="בחר אפשרות" />
              </SelectTrigger>
              <SelectContent>
                {question.options?.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {error && (
              <p className="text-sm text-destructive">{error.message}</p>
            )}
          </div>
        );

      case 'boolean':
        return (
          <div className="space-y-6">
            <div className="flex flex-col gap-4">
              <div className="flex items-center space-x-2 space-x-reverse">
                <Checkbox
                  id="yes"
                  checked={watchedValue === true}
                  onCheckedChange={() => setValue(fieldName, true as any)}
                />
                <Label htmlFor="yes" className="text-lg cursor-pointer">כן</Label>
              </div>
              <div className="flex items-center space-x-2 space-x-reverse">
                <Checkbox
                  id="no"
                  checked={watchedValue === false}
                  onCheckedChange={() => setValue(fieldName, false as any)}
                />
                <Label htmlFor="no" className="text-lg cursor-pointer">לא</Label>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="max-w-md mx-auto">
      <Card>
        <CardHeader className="space-y-4">
          <div className="space-y-2">
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>שאלה {currentStep + 1} מתוך {questions.length}</span>
              <span>{Math.round(progress)}%</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>
          <CardTitle className="text-xl text-center leading-relaxed">
            {currentQuestion.title}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className={cn(
            'transition-all duration-150',
            isAnimating && 'opacity-50 transform translate-x-2'
          )}>
            {renderQuestion()}
          </div>

          <div className="flex justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={handlePrevious}
              className="flex items-center gap-2"
            >
              <ChevronRight className="h-4 w-4" />
              {currentStep === 0 ? 'חזור' : 'קודם'}
            </Button>

            <Button
              type="button"
              onClick={handleNext}
              className="flex items-center gap-2"
            >
              {currentStep === questions.length - 1 ? 'סיום' : 'הבא'}
              <ChevronLeft className="h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
