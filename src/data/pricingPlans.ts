import { PricingPlan } from '@/components/PricingCard';

export const pricingPlans: PricingPlan[] = [
  {
    name: "עסק חדש",
    price: "חינם",
    description: "מושלם לעסקים קטנים שמתחילים את המסע הפיננסי שלהם",
    features: [
      "חשבוניות ללא הגבלה",
      "עד 5 משתמשים בחברה",
      "מעקב הוצאות ללא הגבלה",
      "לוחות דוחות",
      "תמיכה במייל"
    ],
    buttonText: "התחל עכשיו",
    buttonVariant: "outline",
    popular: false
  },
  {
    name: "מתקדם",
    price: "₪99",
    period: "לחודש",
    description: "כל התכונות החינמיות + תכונות AI מתקדמות",
    features: [
      "כל התכונות החינמיות",
      "עוזר AI בוואטסאפ (עד 5 משתמשים)",
      "בודק מיילים AI (עד 100 מסמכים)",
      "אינטגרציה עם משרדי רואי חשבון",
      "תמיכה בוואטסאפ"
    ],
    buttonText: "התחל ניסיון 14 יום",
    buttonVariant: "default",
    popular: true
  },
  {
    name: "ארגוני",
    price: "צור קשר",
    description: "לארגונים גדולים עם פעילות פיננסית מורכבת",
    features: [
      "יכולת התחברות עם API",
      "נציג תמיכה ייעודי",
      "מספרי עוזר AI ללא הגבלה",
      "בודק מיילים ללא הגבלה"
    ],
    buttonText: "צור קשר",
    buttonVariant: "outline",
    popular: false
  }
];

// For onboarding, we need simplified plan data
export const onboardingPlans = pricingPlans.map(plan => ({
  id: plan.name === "עסק חדש" ? "free" : plan.name === "מתקדם" ? "advanced" : "enterprise",
  name: plan.name,
  description: plan.name === "עסק חדש" 
    ? "חשבוניות ללא הגבלה, עד 5 משתמשים" 
    : plan.name === "מתקדם" 
    ? "כל התכונות החינמיות + תכונות AI" 
    : "פתרון מותאם אישית",
  price: plan.price,
  popular: plan.popular
}));
