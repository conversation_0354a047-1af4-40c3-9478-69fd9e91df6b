import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Loader2, AlertCircle, CheckCircle, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import Logo from '@/components/Logo';

const forgotPasswordSchema = z.object({
  email: z.string().email('כתובת אימייל לא תקינה'),
});

type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>;

const ForgotPassword: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [email, setEmail] = useState('');
  
  const { resetPassword } = useAuth();
  const { toast } = useToast();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<ForgotPasswordFormData>({
    resolver: zodResolver(forgotPasswordSchema),
  });

  const onSubmit = async (data: ForgotPasswordFormData) => {
    setIsLoading(true);
    setError(null);

    try {
      const { error } = await resetPassword(data.email);

      if (error) {
        setError(getErrorMessage(error.message));
        return;
      }

      setSuccess(true);
      setEmail(data.email);
      toast({
        title: 'אימייל נשלח בהצלחה',
        description: 'בדוק את תיבת הדואר שלך לקישור איפוס הסיסמה.',
      });
    } catch (err) {
      setError('אירעה שגיאה בלתי צפויה. אנא נסה שוב.');
    } finally {
      setIsLoading(false);
    }
  };

  const getErrorMessage = (errorMessage: string): string => {
    if (errorMessage.includes('User not found')) {
      return 'כתובת האימייל לא נמצאה במערכת';
    }
    if (errorMessage.includes('Email rate limit exceeded')) {
      return 'נשלחו יותר מדי בקשות. אנא נסה שוב מאוחר יותר';
    }
    return 'אירעה שגיאה בשליחת האימייל. אנא נסה שוב';
  };

  if (success) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4 bg-background">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
            <CardTitle className="text-xl">אימייל נשלח בהצלחה!</CardTitle>
            <CardDescription>
              נשלח אימייל עם קישור לאיפוס סיסמה לכתובת:
              <br />
              <strong>{email}</strong>
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-center space-y-4">
              <p className="text-sm text-muted-foreground">
                לא קיבלת אימייל? בדוק בתיקיית הספאם או נסה שוב.
              </p>
              <div className="space-y-2">
                <Button 
                  onClick={() => setSuccess(false)} 
                  variant="outline" 
                  className="w-full"
                >
                  שלח שוב
                </Button>
                <Button asChild className="w-full">
                  <Link to="/signin">
                    <ArrowRight className="mr-2 h-4 w-4" />
                    חזור להתחברות
                  </Link>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-background">
      <div className="w-full max-w-md space-y-6">
        {/* Logo */}
        <div className="text-center">
          <Logo />
          <h1 className="mt-4 text-2xl font-bold">איפוס סיסמה</h1>
          <p className="text-muted-foreground">
            הזן את כתובת האימייל שלך ונשלח לך קישור לאיפוס הסיסמה
          </p>
        </div>

        <Card>
          <CardHeader className="space-y-1">
            <CardTitle className="text-xl text-center">שכחת סיסמה?</CardTitle>
            <CardDescription className="text-center">
              אל תדאג, זה קורה לכולנו. הזן את כתובת האימייל שלך ונשלח לך קישור לאיפוס.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              {error && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <div className="space-y-2">
                <Label htmlFor="email">כתובת אימייל</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  {...register('email')}
                  className={errors.email ? 'border-destructive' : ''}
                  disabled={isLoading}
                />
                {errors.email && (
                  <p className="text-sm text-destructive">{errors.email.message}</p>
                )}
              </div>

              <Button
                type="submit"
                className="w-full"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    שולח...
                  </>
                ) : (
                  'שלח קישור לאיפוס סיסמה'
                )}
              </Button>
            </form>

            <div className="mt-6 text-center">
              <p className="text-sm text-muted-foreground">
                נזכרת בסיסמה?{' '}
                <Link
                  to="/signin"
                  className="text-primary hover:underline font-medium"
                >
                  התחבר כאן
                </Link>
              </p>
            </div>
          </CardContent>
        </Card>

        <div className="text-center">
          <Link
            to="/"
            className="text-sm text-muted-foreground hover:text-foreground"
          >
            ← חזור לעמוד הבית
          </Link>
        </div>
      </div>
    </div>
  );
};

export default ForgotPassword;
