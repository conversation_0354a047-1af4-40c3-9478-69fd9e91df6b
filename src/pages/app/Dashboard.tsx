import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  BarChart3, 
  FileText, 
  Receipt, 
  MessageSquare, 
  Mail, 
  Users,
  TrendingUp,
  DollarSign
} from 'lucide-react';

const Dashboard: React.FC = () => {
  const { userProfile, currentOrganization, userRole } = useAuth();

  const stats = [
    {
      title: 'חשבוניות החודש',
      value: '24',
      change: '+12%',
      icon: FileText,
      color: 'text-blue-600',
    },
    {
      title: 'הוצאות החודש',
      value: '₪18,450',
      change: '-5%',
      icon: Receipt,
      color: 'text-green-600',
    },
    {
      title: 'הודעות WhatsApp',
      value: '156',
      change: '+23%',
      icon: MessageSquare,
      color: 'text-purple-600',
    },
    {
      title: 'סריקות אימייל',
      value: '89',
      change: '+8%',
      icon: Mail,
      color: 'text-orange-600',
    },
  ];

  const quickActions = [
    {
      title: 'חשבונית חדשה',
      description: 'צור חשבונית חדשה',
      icon: FileText,
      href: '/app/invoices/new',
    },
    {
      title: 'הוצאה חדשה',
      description: 'הוסף הוצאה חדשה',
      icon: Receipt,
      href: '/app/expenses/new',
    },
    {
      title: 'דוח חדש',
      description: 'צור דוח מותאם אישית',
      icon: BarChart3,
      href: '/app/reports/new',
    },
    {
      title: 'הגדרות',
      description: 'נהל את הגדרות החשבון',
      icon: Users,
      href: '/app/settings',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">
            שלום, {userProfile?.first_name || 'משתמש'}!
          </h1>
          <p className="text-muted-foreground">
            ברוך הבא ל{currentOrganization?.name} • {userRole}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline">
            <TrendingUp className="mr-2 h-4 w-4" />
            דוח מהיר
          </Button>
          <Button>
            <DollarSign className="mr-2 h-4 w-4" />
            חשבונית חדשה
          </Button>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => (
          <Card key={stat.title}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <stat.icon className={`h-4 w-4 ${stat.color}`} />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p className="text-xs text-muted-foreground">
                {stat.change} מהחודש הקודם
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Quick Actions */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {quickActions.map((action) => (
          <Card key={action.title} className="cursor-pointer hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex items-center space-x-2 space-x-reverse">
                <action.icon className="h-5 w-5 text-primary" />
                <CardTitle className="text-lg">{action.title}</CardTitle>
              </div>
              <CardDescription>{action.description}</CardDescription>
            </CardHeader>
          </Card>
        ))}
      </div>

      {/* Recent Activity */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>פעילות אחרונה</CardTitle>
            <CardDescription>
              הפעולות האחרונות שבוצעו במערכת
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center space-x-4 space-x-reverse">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium">חשבונית חדשה נוצרה</p>
                  <p className="text-xs text-muted-foreground">לפני 2 שעות</p>
                </div>
              </div>
              <div className="flex items-center space-x-4 space-x-reverse">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium">הוצאה אושרה</p>
                  <p className="text-xs text-muted-foreground">לפני 4 שעות</p>
                </div>
              </div>
              <div className="flex items-center space-x-4 space-x-reverse">
                <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium">הודעת WhatsApp התקבלה</p>
                  <p className="text-xs text-muted-foreground">לפני 6 שעות</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>סטטיסטיקות מהירות</CardTitle>
            <CardDescription>
              נתונים חשובים במבט מהיר
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm">חשבוניות ממתינות</span>
                <span className="font-bold">3</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">הוצאות לאישור</span>
                <span className="font-bold">7</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">דוחות מתוזמנים</span>
                <span className="font-bold">2</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">התראות פעילות</span>
                <span className="font-bold">12</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Dashboard;
