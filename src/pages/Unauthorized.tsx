import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Shield, ArrowRight, Home } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuth } from '@/contexts/AuthContext';

const Unauthorized: React.FC = () => {
  const { signOut } = useAuth();
  const navigate = useNavigate();

  const handleSignOut = async () => {
    await signOut();
    navigate('/signin');
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-background">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-destructive/10">
            <Shield className="h-6 w-6 text-destructive" />
          </div>
          <CardTitle className="text-xl">א<PERSON><PERSON> הרשא<PERSON></CardTitle>
          <CardDescription>
            אין לך הרשאה לגשת לעמוד זה. אנא פנה למנהל המערכת או התחבר עם חשבון אחר.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-col gap-2">
            <Button onClick={() => navigate(-1)} variant="outline" className="w-full">
              <ArrowRight className="mr-2 h-4 w-4" />
              חזור לעמוד הקודם
            </Button>
            <Button asChild variant="outline" className="w-full">
              <Link to="/dashboard">
                <Home className="mr-2 h-4 w-4" />
                עמוד הבית
              </Link>
            </Button>
            <Button onClick={handleSignOut} variant="destructive" className="w-full">
              התחבר עם חשבון אחר
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Unauthorized;
