import React, { createContext, useContext, useEffect, useState } from 'react';
import { User, Session, AuthError } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';
import { Tables } from '@/integrations/supabase/types';

type UserProfile = Tables<'user_profiles'>;
type Organization = Tables<'organizations'>;
type OrganizationMember = Tables<'organization_members'>;

interface AuthContextType {
  // Authentication state
  user: User | null;
  session: Session | null;
  userProfile: UserProfile | null;
  
  // Organization state
  currentOrganization: Organization | null;
  organizations: Organization[];
  userRole: string | null;
  
  // Loading states
  loading: boolean;
  profileLoading: boolean;
  organizationsLoading: boolean;
  
  // Authentication methods
  signIn: (email: string, password: string) => Promise<{ error: AuthError | null }>;
  signUp: (email: string, password: string, userData?: Partial<UserProfile>) => Promise<{ error: AuthError | null }>;
  signOut: () => Promise<{ error: AuthError | null }>;
  resetPassword: (email: string) => Promise<{ error: AuthError | null }>;
  
  // Organization methods
  switchOrganization: (organizationId: string) => Promise<void>;
  createOrganization: (name: string, slug: string) => Promise<{ data: Organization | null; error: any }>;
  
  // Profile methods
  updateProfile: (updates: Partial<UserProfile>) => Promise<{ error: any }>;
  
  // Utility methods
  hasPermission: (permission: string) => boolean;
  isOwner: () => boolean;
  isAdmin: () => boolean;
  isManager: () => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  // Authentication state
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  
  // Organization state
  const [currentOrganization, setCurrentOrganization] = useState<Organization | null>(null);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [userRole, setUserRole] = useState<string | null>(null);
  
  // Loading states
  const [loading, setLoading] = useState(true);
  const [profileLoading, setProfileLoading] = useState(false);
  const [organizationsLoading, setOrganizationsLoading] = useState(false);

  // Initialize auth state
  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setUser(session?.user ?? null);
      if (session?.user) {
        loadUserProfile(session.user.id);
        loadUserOrganizations(session.user.id);
      }
      setLoading(false);
    });

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      setSession(session);
      setUser(session?.user ?? null);
      
      if (session?.user) {
        await loadUserProfile(session.user.id);
        await loadUserOrganizations(session.user.id);
      } else {
        // Clear state on sign out
        setUserProfile(null);
        setCurrentOrganization(null);
        setOrganizations([]);
        setUserRole(null);
      }
      setLoading(false);
    });

    return () => subscription.unsubscribe();
  }, []);

  // Load user profile
  const loadUserProfile = async (userId: string) => {
    setProfileLoading(true);
    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('Error loading user profile:', error);
        return;
      }

      if (data) {
        setUserProfile(data);
      }
    } catch (error) {
      console.error('Error loading user profile:', error);
    } finally {
      setProfileLoading(false);
    }
  };

  // Load user organizations
  const loadUserOrganizations = async (userId: string) => {
    setOrganizationsLoading(true);
    try {
      const { data: memberships, error } = await supabase
        .from('organization_members')
        .select(`
          role,
          organization_id,
          organizations (*)
        `)
        .eq('user_id', userId)
        .eq('is_active', true);

      if (error) {
        console.error('Error loading organizations:', error);
        return;
      }

      if (memberships) {
        const orgs = memberships.map((m: any) => m.organizations).filter(Boolean);
        setOrganizations(orgs);
        
        // Set current organization (first one or from localStorage)
        const savedOrgId = localStorage.getItem('currentOrganizationId');
        const currentOrg = savedOrgId 
          ? orgs.find((org: Organization) => org.id === savedOrgId)
          : orgs[0];
          
        if (currentOrg) {
          setCurrentOrganization(currentOrg);
          const membership = memberships.find((m: any) => m.organization_id === currentOrg.id);
          setUserRole(membership?.role || null);
          localStorage.setItem('currentOrganizationId', currentOrg.id);
        }
      }
    } catch (error) {
      console.error('Error loading organizations:', error);
    } finally {
      setOrganizationsLoading(false);
    }
  };

  // Authentication methods
  const signIn = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    return { error };
  };

  const signUp = async (email: string, password: string, userData?: Partial<UserProfile>) => {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
    });

    if (!error && data.user && userData) {
      // Create user profile
      const { error: profileError } = await supabase
        .from('user_profiles')
        .insert({
          id: data.user.id,
          email: data.user.email!,
          ...userData,
        });

      if (profileError) {
        console.error('Error creating user profile:', profileError);
      }
    }

    return { error };
  };

  const signOut = async () => {
    const { error } = await supabase.auth.signOut();
    if (!error) {
      localStorage.removeItem('currentOrganizationId');
    }
    return { error };
  };

  const resetPassword = async (email: string) => {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/reset-password`,
    });
    return { error };
  };

  // Organization methods
  const switchOrganization = async (organizationId: string) => {
    const org = organizations.find(o => o.id === organizationId);
    if (org) {
      setCurrentOrganization(org);
      localStorage.setItem('currentOrganizationId', organizationId);
      
      // Update user role for this organization
      const { data: membership } = await supabase
        .from('organization_members')
        .select('role')
        .eq('user_id', user!.id)
        .eq('organization_id', organizationId)
        .eq('is_active', true)
        .single();
        
      setUserRole(membership?.role || null);
    }
  };

  const createOrganization = async (name: string, slug: string) => {
    if (!user) return { data: null, error: 'User not authenticated' };

    const { data: org, error: orgError } = await supabase
      .from('organizations')
      .insert({
        name,
        slug,
      })
      .select()
      .single();

    if (orgError) return { data: null, error: orgError };

    // Add user as owner
    const { error: memberError } = await supabase
      .from('organization_members')
      .insert({
        organization_id: org.id,
        user_id: user.id,
        role: 'owner',
      });

    if (memberError) return { data: null, error: memberError };

    // Reload organizations
    await loadUserOrganizations(user.id);

    return { data: org, error: null };
  };

  // Profile methods
  const updateProfile = async (updates: Partial<UserProfile>) => {
    if (!user) return { error: 'User not authenticated' };

    const { error } = await supabase
      .from('user_profiles')
      .update(updates)
      .eq('id', user.id);

    if (!error) {
      setUserProfile(prev => prev ? { ...prev, ...updates } : null);
    }

    return { error };
  };

  // Utility methods
  const hasPermission = (permission: string): boolean => {
    if (!userRole) return false;
    
    const roleHierarchy = {
      owner: 4,
      admin: 3,
      manager: 2,
      user: 1,
    };

    const permissionLevels = {
      'read': 1,
      'write': 2,
      'manage': 3,
      'admin': 4,
    };

    const userLevel = roleHierarchy[userRole as keyof typeof roleHierarchy] || 0;
    const requiredLevel = permissionLevels[permission as keyof typeof permissionLevels] || 0;

    return userLevel >= requiredLevel;
  };

  const isOwner = () => userRole === 'owner';
  const isAdmin = () => userRole === 'admin' || userRole === 'owner';
  const isManager = () => ['manager', 'admin', 'owner'].includes(userRole || '');

  const value: AuthContextType = {
    // Authentication state
    user,
    session,
    userProfile,
    
    // Organization state
    currentOrganization,
    organizations,
    userRole,
    
    // Loading states
    loading,
    profileLoading,
    organizationsLoading,
    
    // Authentication methods
    signIn,
    signUp,
    signOut,
    resetPassword,
    
    // Organization methods
    switchOrganization,
    createOrganization,
    
    // Profile methods
    updateProfile,
    
    // Utility methods
    hasPermission,
    isOwner,
    isAdmin,
    isManager,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
