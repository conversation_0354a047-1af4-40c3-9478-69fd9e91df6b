-- Enable Row Level Security on all tables
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE organization_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE expenses ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_chat_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_chat_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_integrations ENABLE ROW LEVEL SECURITY;
ALTER TABLE reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;

-- Helper function to get user's organizations
CREATE OR REPLACE FUNCTION get_user_organizations(user_uuid UUID)
RETURNS TABLE(organization_id UUID, role user_role) AS $$
BEGIN
    RETURN QUERY
    SELECT om.organization_id, om.role
    FROM organization_members om
    WHERE om.user_id = user_uuid AND om.is_active = true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to check if user belongs to organization
CREATE OR REPLACE FUNCTION user_belongs_to_organization(user_uuid UUID, org_uuid UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM organization_members om
        WHERE om.user_id = user_uuid 
        AND om.organization_id = org_uuid 
        AND om.is_active = true
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to check user role in organization
CREATE OR REPLACE FUNCTION user_has_role_in_organization(user_uuid UUID, org_uuid UUID, required_role user_role)
RETURNS BOOLEAN AS $$
DECLARE
    user_role_level INTEGER;
    required_role_level INTEGER;
BEGIN
    -- Define role hierarchy: owner=4, admin=3, manager=2, user=1
    SELECT CASE 
        WHEN om.role = 'owner' THEN 4
        WHEN om.role = 'admin' THEN 3
        WHEN om.role = 'manager' THEN 2
        WHEN om.role = 'user' THEN 1
        ELSE 0
    END INTO user_role_level
    FROM organization_members om
    WHERE om.user_id = user_uuid AND om.organization_id = org_uuid AND om.is_active = true;
    
    required_role_level := CASE 
        WHEN required_role = 'owner' THEN 4
        WHEN required_role = 'admin' THEN 3
        WHEN required_role = 'manager' THEN 2
        WHEN required_role = 'user' THEN 1
        ELSE 0
    END;
    
    RETURN COALESCE(user_role_level, 0) >= required_role_level;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Organizations RLS Policies
CREATE POLICY "Users can view organizations they belong to" ON organizations
    FOR SELECT USING (
        id IN (SELECT organization_id FROM get_user_organizations(auth.uid()))
    );

CREATE POLICY "Organization owners can update their organization" ON organizations
    FOR UPDATE USING (
        user_has_role_in_organization(auth.uid(), id, 'owner')
    );

CREATE POLICY "Organization owners can delete their organization" ON organizations
    FOR DELETE USING (
        user_has_role_in_organization(auth.uid(), id, 'owner')
    );

CREATE POLICY "Authenticated users can create organizations" ON organizations
    FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);

-- User Profiles RLS Policies
CREATE POLICY "Users can view their own profile" ON user_profiles
    FOR SELECT USING (id = auth.uid());

CREATE POLICY "Users can update their own profile" ON user_profiles
    FOR UPDATE USING (id = auth.uid());

CREATE POLICY "Users can insert their own profile" ON user_profiles
    FOR INSERT WITH CHECK (id = auth.uid());

-- Organization Members RLS Policies
CREATE POLICY "Users can view members of their organizations" ON organization_members
    FOR SELECT USING (
        user_belongs_to_organization(auth.uid(), organization_id)
    );

CREATE POLICY "Organization admins can manage members" ON organization_members
    FOR ALL USING (
        user_has_role_in_organization(auth.uid(), organization_id, 'admin')
    );

CREATE POLICY "Users can join organizations when invited" ON organization_members
    FOR INSERT WITH CHECK (
        user_id = auth.uid() OR 
        user_has_role_in_organization(auth.uid(), organization_id, 'admin')
    );

-- Invoices RLS Policies
CREATE POLICY "Users can view invoices from their organizations" ON invoices
    FOR SELECT USING (
        user_belongs_to_organization(auth.uid(), organization_id)
    );

CREATE POLICY "Users can create invoices in their organizations" ON invoices
    FOR INSERT WITH CHECK (
        user_belongs_to_organization(auth.uid(), organization_id) AND
        created_by = auth.uid()
    );

CREATE POLICY "Users can update invoices they created or managers can update any" ON invoices
    FOR UPDATE USING (
        (created_by = auth.uid() AND user_belongs_to_organization(auth.uid(), organization_id)) OR
        user_has_role_in_organization(auth.uid(), organization_id, 'manager')
    );

CREATE POLICY "Managers can delete invoices in their organizations" ON invoices
    FOR DELETE USING (
        user_has_role_in_organization(auth.uid(), organization_id, 'manager')
    );

-- Expenses RLS Policies
CREATE POLICY "Users can view expenses from their organizations" ON expenses
    FOR SELECT USING (
        user_belongs_to_organization(auth.uid(), organization_id)
    );

CREATE POLICY "Users can create expenses in their organizations" ON expenses
    FOR INSERT WITH CHECK (
        user_belongs_to_organization(auth.uid(), organization_id) AND
        created_by = auth.uid()
    );

CREATE POLICY "Users can update expenses they created or managers can update any" ON expenses
    FOR UPDATE USING (
        (created_by = auth.uid() AND user_belongs_to_organization(auth.uid(), organization_id)) OR
        user_has_role_in_organization(auth.uid(), organization_id, 'manager')
    );

CREATE POLICY "Managers can delete expenses in their organizations" ON expenses
    FOR DELETE USING (
        user_has_role_in_organization(auth.uid(), organization_id, 'manager')
    );

-- AI Chat Sessions RLS Policies
CREATE POLICY "Users can view their own chat sessions" ON ai_chat_sessions
    FOR SELECT USING (
        user_id = auth.uid() AND user_belongs_to_organization(auth.uid(), organization_id)
    );

CREATE POLICY "Users can create chat sessions in their organizations" ON ai_chat_sessions
    FOR INSERT WITH CHECK (
        user_id = auth.uid() AND user_belongs_to_organization(auth.uid(), organization_id)
    );

CREATE POLICY "Users can update their own chat sessions" ON ai_chat_sessions
    FOR UPDATE USING (
        user_id = auth.uid() AND user_belongs_to_organization(auth.uid(), organization_id)
    );

-- AI Chat Messages RLS Policies
CREATE POLICY "Users can view messages from their chat sessions" ON ai_chat_messages
    FOR SELECT USING (
        session_id IN (
            SELECT id FROM ai_chat_sessions 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can create messages in their chat sessions" ON ai_chat_messages
    FOR INSERT WITH CHECK (
        session_id IN (
            SELECT id FROM ai_chat_sessions 
            WHERE user_id = auth.uid()
        )
    );

-- Email Integrations RLS Policies
CREATE POLICY "Users can view their own email integrations" ON email_integrations
    FOR SELECT USING (
        user_id = auth.uid() AND user_belongs_to_organization(auth.uid(), organization_id)
    );

CREATE POLICY "Users can manage their own email integrations" ON email_integrations
    FOR ALL USING (
        user_id = auth.uid() AND user_belongs_to_organization(auth.uid(), organization_id)
    );

-- Reports RLS Policies
CREATE POLICY "Users can view reports from their organizations" ON reports
    FOR SELECT USING (
        user_belongs_to_organization(auth.uid(), organization_id)
    );

CREATE POLICY "Users can create reports in their organizations" ON reports
    FOR INSERT WITH CHECK (
        user_belongs_to_organization(auth.uid(), organization_id) AND
        created_by = auth.uid()
    );

CREATE POLICY "Users can update reports they created or managers can update any" ON reports
    FOR UPDATE USING (
        (created_by = auth.uid() AND user_belongs_to_organization(auth.uid(), organization_id)) OR
        user_has_role_in_organization(auth.uid(), organization_id, 'manager')
    );

-- Audit Logs RLS Policies
CREATE POLICY "Admins can view audit logs from their organizations" ON audit_logs
    FOR SELECT USING (
        user_has_role_in_organization(auth.uid(), organization_id, 'admin')
    );

CREATE POLICY "System can insert audit logs" ON audit_logs
    FOR INSERT WITH CHECK (true);

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;
